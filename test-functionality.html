<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Column Mapping Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-md-8">
                <!-- Embed the main application -->
                <iframe src="index.html" width="100%" height="600" style="border: 1px solid #ccc;"></iframe>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5>Test Results</h5>
                    </div>
                    <div class="card-body">
                        <div id="testResults">
                            <div class="alert alert-info">
                                <h6>Manual Test Checklist:</h6>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="test1">
                                    <label class="form-check-label" for="test1">
                                        File import works for left panel
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="test2">
                                    <label class="form-check-label" for="test2">
                                        File import works for right panel
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="test3">
                                    <label class="form-check-label" for="test3">
                                        Columns display correctly after import
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="test4">
                                    <label class="form-check-label" for="test4">
                                        Left column selection works (blue highlight)
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="test5">
                                    <label class="form-check-label" for="test5">
                                        Connection creation works (green line drawn)
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="test6">
                                    <label class="form-check-label" for="test6">
                                        Connected columns show green indicators
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="test7">
                                    <label class="form-check-label" for="test7">
                                        Line deletion works (click on line)
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="test8">
                                    <label class="form-check-label" for="test8">
                                        Clear all connections works
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="test9">
                                    <label class="form-check-label" for="test9">
                                        Save/Load functionality works
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="test10">
                                    <label class="form-check-label" for="test10">
                                        Status messages update correctly
                                    </label>
                                </div>
                            </div>
                            
                            <div class="mt-3">
                                <h6>Test Files Available:</h6>
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item">test_columns.csv - Simple test data</li>
                                    <li class="list-group-item">target_columns.csv - Target columns</li>
                                    <li class="list-group-item">sample_files/June 18 PG CSV.csv - Real data</li>
                                </ul>
                            </div>
                            
                            <div class="mt-3">
                                <button class="btn btn-primary" onclick="runAutomatedTests()">
                                    Run Automated Tests
                                </button>
                                <div id="automatedResults" class="mt-2"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function runAutomatedTests() {
            const resultsDiv = document.getElementById('automatedResults');
            resultsDiv.innerHTML = '<div class="alert alert-info">Running tests...</div>';
            
            // Test if the main application loaded
            const iframe = document.querySelector('iframe');
            let testResults = [];
            
            try {
                // Test 1: Check if iframe loaded
                if (iframe.contentWindow) {
                    testResults.push({test: 'Iframe loaded', status: 'PASS'});
                } else {
                    testResults.push({test: 'Iframe loaded', status: 'FAIL'});
                }
                
                // Test 2: Check if main elements exist in iframe
                setTimeout(() => {
                    try {
                        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                        const leftInput = iframeDoc.getElementById('leftFileInput');
                        const rightInput = iframeDoc.getElementById('rightFileInput');
                        const canvas = iframeDoc.getElementById('connectionCanvas');
                        
                        if (leftInput) testResults.push({test: 'Left file input exists', status: 'PASS'});
                        else testResults.push({test: 'Left file input exists', status: 'FAIL'});
                        
                        if (rightInput) testResults.push({test: 'Right file input exists', status: 'PASS'});
                        else testResults.push({test: 'Right file input exists', status: 'FAIL'});
                        
                        if (canvas) testResults.push({test: 'Canvas element exists', status: 'PASS'});
                        else testResults.push({test: 'Canvas element exists', status: 'FAIL'});
                        
                        // Display results
                        let html = '<div class="alert alert-success"><h6>Automated Test Results:</h6><ul>';
                        testResults.forEach(result => {
                            const color = result.status === 'PASS' ? 'text-success' : 'text-danger';
                            html += `<li class="${color}">${result.test}: ${result.status}</li>`;
                        });
                        html += '</ul></div>';
                        resultsDiv.innerHTML = html;
                        
                    } catch (e) {
                        resultsDiv.innerHTML = '<div class="alert alert-warning">Cross-origin restrictions prevent automated testing. Please test manually.</div>';
                    }
                }, 1000);
                
            } catch (e) {
                resultsDiv.innerHTML = '<div class="alert alert-danger">Error running tests: ' + e.message + '</div>';
            }
        }
    </script>
</body>
</html>
