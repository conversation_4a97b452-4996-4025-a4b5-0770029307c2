class ColumnMappingApp {
    constructor() {
        this.leftItems = [];
        this.rightItems = [];
        this.connections = [];
        this.selectedLeftItem = null;
        this.isDrawing = false;
        this.isDragging = false;
        this.dragStartItem = null;
        this.drawingLine = null;
        this.mousePosition = { x: 0, y: 0 };
        this.canvas = null;
        this.ctx = null;
        
        this.init();
    }
    
    init() {
        console.log('Initializing ColumnMappingApp');

        // Debug: Check if HTML elements exist
        console.log('Checking HTML elements...');
        const leftInput = document.getElementById('leftFileInput');
        const rightInput = document.getElementById('rightFileInput');
        console.log('HTML elements found:', {
            leftFileInput: leftInput,
            rightFileInput: rightInput,
            leftInputExists: !!leftInput,
            rightInputExists: !!rightInput
        });

        this.setupCanvas();
        this.setupEventListeners();
        this.loadFromStorage();
        this.updateStatus('Ready to import CSV files. You can click or drag columns to create connections.');
        console.log('ColumnMappingApp initialized successfully');
    }
    
    setupCanvas() {
        this.canvas = document.getElementById('connectionCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.resizeCanvas();
        
        window.addEventListener('resize', () => this.resizeCanvas());
    }
    
    resizeCanvas() {
        const container = document.querySelector('.row.g-3.position-relative');
        if (container) {
            this.canvas.width = container.offsetWidth;
            this.canvas.height = container.offsetHeight;
            this.redrawConnections();
        }
    }
    
    setupEventListeners() {
        console.log('Setting up event listeners...');

        // File input listeners using Method 1 (direct HTML file inputs)
        const leftFileInput = document.getElementById('leftFileInput');
        const rightFileInput = document.getElementById('rightFileInput');

        console.log('Found elements:', {
            leftFileInput: leftFileInput,
            rightFileInput: rightFileInput
        });

        if (leftFileInput) {
            console.log('Adding event listeners to left file input');

            // Add click listener to debug
            leftFileInput.addEventListener('click', () => {
                console.log('Left file input clicked - file dialog should open');
            });

            leftFileInput.addEventListener('change', (e) => {
                console.log('Left file input changed', e.target.files);
                if (e.target.files.length > 0) {
                    console.log('File selected:', e.target.files[0].name);
                    this.handleFileImport(e, 'left');
                } else {
                    console.log('No file selected');
                }
            });

            console.log('Left file input event listeners added successfully');
        } else {
            console.error('Left file input not found');
        }

        if (rightFileInput) {
            console.log('Adding event listeners to right file input');

            // Add click listener to debug
            rightFileInput.addEventListener('click', () => {
                console.log('Right file input clicked - file dialog should open');
            });

            rightFileInput.addEventListener('change', (e) => {
                console.log('Right file input changed', e.target.files);
                if (e.target.files.length > 0) {
                    console.log('File selected:', e.target.files[0].name);
                    this.handleFileImport(e, 'right');
                } else {
                    console.log('No file selected');
                }
            });

            console.log('Right file input event listeners added successfully');
        } else {
            console.error('Right file input not found');
        }

        // Control button listeners
        const clearAllBtn = document.getElementById('clearAllBtn');
        const saveBtn = document.getElementById('saveBtn');
        const loadBtn = document.getElementById('loadBtn');

        if (clearAllBtn) {
            clearAllBtn.addEventListener('click', () => {
                this.clearAllConnections();
            });
        }

        if (saveBtn) {
            saveBtn.addEventListener('click', () => {
                this.saveToStorage();
            });
        }

        if (loadBtn) {
            loadBtn.addEventListener('click', () => {
                this.loadFromStorage();
            });
        }

        // Canvas listeners for line deletion and drawing
        if (this.canvas) {
            this.canvas.addEventListener('click', (e) => {
                this.handleCanvasClick(e);
            });

            // Mouse tracking for live line drawing
            this.canvas.addEventListener('mousemove', (e) => {
                this.handleMouseMove(e);
            });

            // Also track mouse on the main container
            const mainContainer = document.querySelector('.row.g-3.position-relative');
            if (mainContainer) {
                mainContainer.addEventListener('mousemove', (e) => {
                    this.handleMouseMove(e);
                });

                mainContainer.addEventListener('mouseleave', () => {
                    this.handleMouseLeave();
                });
            }
        }

        // Add keyboard listener for escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isDrawing) {
                this.clearSelection();
                this.updateStatus('Drawing cancelled. Click a left column to start again.');
            }
        });
    }

    handleFileImport(event, side) {
        const file = event.target.files[0];
        if (!file) return;
        
        const reader = new FileReader();
        reader.onload = (e) => {
            const csvContent = e.target.result;
            const columns = this.parseCSV(csvContent);
            
            if (side === 'left') {
                this.leftItems = columns;
                document.getElementById('leftFileName').textContent = file.name;
                this.renderItems('leftItems', columns, 'left');
            } else {
                this.rightItems = columns;
                document.getElementById('rightFileName').textContent = file.name;
                this.renderItems('rightItems', columns, 'right');
            }
            
            this.updateStatus(`Imported ${columns.length} columns from ${file.name}`);
        };
        
        reader.readAsText(file);
    }
    
    parseCSV(csvContent) {
        const lines = csvContent.trim().split('\n');
        if (lines.length === 0) return [];
        
        const firstLine = lines[0];
        let columns = [];
        
        // Try different delimiters
        if (firstLine.includes(',')) {
            columns = this.parseCSVLine(firstLine, ',');
        } else if (firstLine.includes('|')) {
            columns = this.parseCSVLine(firstLine, '|');
        } else if (firstLine.includes('\t')) {
            columns = this.parseCSVLine(firstLine, '\t');
        } else {
            // Fallback: split by comma
            columns = firstLine.split(',');
        }
        
        // Clean up column names
        return columns.map(col => col.trim().replace(/^["']|["']$/g, ''));
    }
    
    parseCSVLine(line, delimiter) {
        const result = [];
        let current = '';
        let inQuotes = false;
        
        for (let i = 0; i < line.length; i++) {
            const char = line[i];
            
            if (char === '"' || char === "'") {
                inQuotes = !inQuotes;
            } else if (char === delimiter && !inQuotes) {
                result.push(current);
                current = '';
            } else {
                current += char;
            }
        }
        
        result.push(current);
        return result;
    }
    
    renderItems(containerId, items, side) {
        const container = document.getElementById(containerId);
        container.innerHTML = '';

        if (items.length === 0) {
            const placeholderIcon = side === 'left' ? 'bi-upload' : 'bi-upload';
            container.innerHTML = `
                <div class="text-center text-muted fst-italic py-5">
                    <i class="bi ${placeholderIcon} fs-1 d-block mb-2"></i>
                    Import a CSV file to see columns
                </div>
            `;
            return;
        }

        items.forEach((item, index) => {
            const itemElement = document.createElement('div');
            itemElement.className = 'item';
            itemElement.style.cursor = 'pointer';
            itemElement.innerHTML = `
                <div class="item-content">
                    <span class="item-icon">⚪</span>
                    <span class="item-text">${item}</span>
                </div>
            `;
            itemElement.dataset.index = index;
            itemElement.dataset.side = side;

            // Add click listener for connection creation (fallback)
            itemElement.addEventListener('click', (e) => {
                this.handleItemClick(e, side, index);
            });

            // Add drag and drop listeners for left items
            if (side === 'left') {
                itemElement.draggable = true;
                itemElement.addEventListener('dragstart', (e) => {
                    this.handleDragStart(e, side, index);
                });
                itemElement.addEventListener('dragend', (e) => {
                    this.handleDragEnd(e);
                });
            }

            // Add drop listeners for right items
            if (side === 'right') {
                itemElement.addEventListener('dragover', (e) => {
                    this.handleDragOver(e);
                });
                itemElement.addEventListener('dragleave', (e) => {
                    this.handleDragLeave(e);
                });
                itemElement.addEventListener('drop', (e) => {
                    this.handleDrop(e, side, index);
                });
            }

            container.appendChild(itemElement);
        });
    }
    
    handleItemClick(event, side, index) {
        const itemElement = event.currentTarget;

        if (side === 'left') {
            // Select left item for connection and start interactive drawing
            this.clearSelection();
            this.selectedLeftItem = { element: itemElement, index: index };
            itemElement.classList.add('selected');
            // Update icon to show selection
            const icon = itemElement.querySelector('.item-icon');
            if (icon) {
                icon.textContent = '✅';
            }

            // Start interactive line drawing
            this.startInteractiveDrawing(itemElement);
            this.updateStatus('Move your mouse to draw a line, then click a right column to connect.');
        } else if (side === 'right' && this.selectedLeftItem) {
            // Create connection and stop drawing
            this.createConnection(this.selectedLeftItem.index, index);
            this.stopInteractiveDrawing();
            this.clearSelection();
            this.updateStatus('Connection created!');
        }
    }

    handleDragStart(event, side, index) {
        this.isDragging = true;
        this.dragStartItem = { side: side, index: index };

        // Visual feedback for drag start
        event.currentTarget.classList.add('dragging');

        // Set drag data
        event.dataTransfer.effectAllowed = 'link';
        event.dataTransfer.setData('text/plain', `${side}-${index}`);

        this.updateStatus('Drag to a column on the right to create connection...');
    }

    handleDragEnd(event) {
        this.isDragging = false;

        // Reset visual feedback
        event.currentTarget.classList.remove('dragging');

        // Remove drag-over effects from all right items
        document.querySelectorAll('[data-side="right"]').forEach(item => {
            item.classList.remove('drag-over');
        });

        this.dragStartItem = null;
        this.updateStatus('Drag ended. You can click columns or drag again to create connections.');
    }

    handleDragOver(event) {
        event.preventDefault();
        event.dataTransfer.dropEffect = 'link';

        // Visual feedback for valid drop target
        event.currentTarget.classList.add('drag-over');
    }

    handleDragLeave(event) {
        // Remove drag-over effect when leaving the drop target
        event.currentTarget.classList.remove('drag-over');
    }

    handleDrop(event, side, index) {
        event.preventDefault();

        // Remove visual feedback
        event.currentTarget.classList.remove('drag-over');

        if (this.dragStartItem && this.dragStartItem.side === 'left' && side === 'right') {
            // Create connection from dragged left item to dropped right item
            this.createConnection(this.dragStartItem.index, index);
            this.updateStatus('Connection created via drag and drop!');
        }

        this.isDragging = false;
        this.dragStartItem = null;
    }

    startInteractiveDrawing(leftElement) {
        this.isDrawing = true;

        // Store the starting point for the line
        const leftRect = leftElement.getBoundingClientRect();
        const canvasRect = this.canvas.getBoundingClientRect();

        this.drawingLine = {
            startX: leftRect.right - canvasRect.left,
            startY: leftRect.top + leftRect.height / 2 - canvasRect.top,
            endX: leftRect.right - canvasRect.left,
            endY: leftRect.top + leftRect.height / 2 - canvasRect.top
        };

        // Enable mouse tracking
        this.canvas.style.pointerEvents = 'auto';
    }

    stopInteractiveDrawing() {
        this.isDrawing = false;
        this.drawingLine = null;
        this.redrawConnections(); // Redraw without the temporary line
    }

    handleMouseMove(event) {
        if (!this.isDrawing || !this.drawingLine) return;

        const canvasRect = this.canvas.getBoundingClientRect();
        this.mousePosition.x = event.clientX - canvasRect.left;
        this.mousePosition.y = event.clientY - canvasRect.top;

        // Update the end point of the drawing line
        this.drawingLine.endX = this.mousePosition.x;
        this.drawingLine.endY = this.mousePosition.y;

        // Redraw all connections plus the interactive line
        this.redrawConnectionsWithInteractive();
    }

    handleMouseLeave() {
        if (this.isDrawing && this.drawingLine) {
            // Keep the line at the last known position when mouse leaves
            this.redrawConnectionsWithInteractive();
        }
    }

    redrawConnectionsWithInteractive() {
        if (!this.ctx) return;

        // Clear canvas
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

        // Draw existing connections
        this.connections.forEach(conn => {
            this.drawConnection(conn);
        });

        // Draw the interactive line if we're drawing
        if (this.isDrawing && this.drawingLine) {
            this.drawInteractiveLine();
        }
    }

    drawInteractiveLine() {
        if (!this.drawingLine) return;

        this.ctx.beginPath();
        this.ctx.moveTo(this.drawingLine.startX, this.drawingLine.startY);
        this.ctx.lineTo(this.drawingLine.endX, this.drawingLine.endY);
        this.ctx.strokeStyle = '#2196f3'; // Blue color for interactive line
        this.ctx.lineWidth = 3;
        this.ctx.setLineDash([5, 5]); // Dashed line
        this.ctx.stroke();
        this.ctx.setLineDash([]); // Reset line dash

        // Draw starting point
        this.ctx.beginPath();
        this.ctx.arc(this.drawingLine.startX, this.drawingLine.startY, 6, 0, 2 * Math.PI);
        this.ctx.fillStyle = '#2196f3';
        this.ctx.fill();

        // Draw end point (cursor position)
        this.ctx.beginPath();
        this.ctx.arc(this.drawingLine.endX, this.drawingLine.endY, 4, 0, 2 * Math.PI);
        this.ctx.fillStyle = '#2196f3';
        this.ctx.fill();
    }

    createConnection(leftIndex, rightIndex) {
        // Check if connection already exists
        const existingConnection = this.connections.find(conn => 
            conn.left === leftIndex && conn.right === rightIndex
        );
        
        if (existingConnection) {
            this.updateStatus('Connection already exists!');
            return;
        }
        
        this.connections.push({
            left: leftIndex,
            right: rightIndex,
            id: Date.now() + Math.random()
        });
        
        this.updateConnectedItems();
        this.redrawConnections();
        this.updateConnectionCount();
    }
    
    clearSelection() {
        document.querySelectorAll('.selected').forEach(item => {
            item.classList.remove('selected');
            // Reset icon
            const icon = item.querySelector('.item-icon');
            if (icon) {
                icon.textContent = '⚪';
            }
        });
        this.selectedLeftItem = null;

        // Stop interactive drawing if active
        if (this.isDrawing) {
            this.stopInteractiveDrawing();
        }
    }
    
    updateConnectedItems() {
        // Update visual indicators for connected items
        document.querySelectorAll('.item').forEach(item => {
            item.classList.remove('connected');
            // Reset connected icons
            const icon = item.querySelector('.item-icon');
            if (icon && !item.classList.contains('selected')) {
                icon.textContent = '⚪';
            }
        });

        this.connections.forEach(conn => {
            const leftItem = document.querySelector(`[data-side="left"][data-index="${conn.left}"]`);
            const rightItem = document.querySelector(`[data-side="right"][data-index="${conn.right}"]`);

            if (leftItem && !leftItem.classList.contains('selected')) {
                leftItem.classList.add('connected');
                const icon = leftItem.querySelector('.item-icon');
                if (icon) {
                    icon.textContent = '🔗';
                }
            }
            if (rightItem) {
                rightItem.classList.add('connected');
                const icon = rightItem.querySelector('.item-icon');
                if (icon) {
                    icon.textContent = '🔗';
                }
            }
        });
    }
    
    redrawConnections() {
        if (!this.ctx) return;
        
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        this.connections.forEach(conn => {
            this.drawConnection(conn);
        });
    }
    
    drawConnection(connection) {
        const leftItem = document.querySelector(`[data-side="left"][data-index="${connection.left}"]`);
        const rightItem = document.querySelector(`[data-side="right"][data-index="${connection.right}"]`);

        if (!leftItem || !rightItem) return;

        const leftRect = leftItem.getBoundingClientRect();
        const rightRect = rightItem.getBoundingClientRect();
        const canvasRect = this.canvas.getBoundingClientRect();

        const startX = leftRect.right - canvasRect.left;
        const startY = leftRect.top + leftRect.height / 2 - canvasRect.top;
        const endX = rightRect.left - canvasRect.left;
        const endY = rightRect.top + rightRect.height / 2 - canvasRect.top;

        this.ctx.beginPath();
        this.ctx.moveTo(startX, startY);
        this.ctx.lineTo(endX, endY);
        this.ctx.strokeStyle = '#4caf50';
        this.ctx.lineWidth = 2;
        this.ctx.stroke();

        // Draw connection points
        this.ctx.beginPath();
        this.ctx.arc(startX, startY, 4, 0, 2 * Math.PI);
        this.ctx.fillStyle = '#4caf50';
        this.ctx.fill();

        this.ctx.beginPath();
        this.ctx.arc(endX, endY, 4, 0, 2 * Math.PI);
        this.ctx.fill();
    }

    handleCanvasClick(event) {
        const rect = this.canvas.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;

        // Check if click is near any connection line
        for (let i = 0; i < this.connections.length; i++) {
            const conn = this.connections[i];
            if (this.isClickNearConnection(x, y, conn)) {
                this.deleteConnection(i);
                this.updateStatus('Connection deleted!');
                break;
            }
        }
    }

    isClickNearConnection(clickX, clickY, connection) {
        const leftItem = document.querySelector(`[data-side="left"][data-index="${connection.left}"]`);
        const rightItem = document.querySelector(`[data-side="right"][data-index="${connection.right}"]`);

        if (!leftItem || !rightItem) return false;

        const leftRect = leftItem.getBoundingClientRect();
        const rightRect = rightItem.getBoundingClientRect();
        const canvasRect = this.canvas.getBoundingClientRect();

        const startX = leftRect.right - canvasRect.left;
        const startY = leftRect.top + leftRect.height / 2 - canvasRect.top;
        const endX = rightRect.left - canvasRect.left;
        const endY = rightRect.top + rightRect.height / 2 - canvasRect.top;

        // Calculate distance from click point to line
        const distance = this.distanceToLine(clickX, clickY, startX, startY, endX, endY);
        return distance < 10; // 10 pixel tolerance
    }

    distanceToLine(px, py, x1, y1, x2, y2) {
        const A = px - x1;
        const B = py - y1;
        const C = x2 - x1;
        const D = y2 - y1;

        const dot = A * C + B * D;
        const lenSq = C * C + D * D;

        if (lenSq === 0) return Math.sqrt(A * A + B * B);

        const param = dot / lenSq;

        let xx, yy;
        if (param < 0) {
            xx = x1;
            yy = y1;
        } else if (param > 1) {
            xx = x2;
            yy = y2;
        } else {
            xx = x1 + param * C;
            yy = y1 + param * D;
        }

        const dx = px - xx;
        const dy = py - yy;
        return Math.sqrt(dx * dx + dy * dy);
    }

    deleteConnection(index) {
        this.connections.splice(index, 1);
        this.updateConnectedItems();
        this.redrawConnections();
        this.updateConnectionCount();
    }

    clearAllConnections() {
        this.connections = [];
        this.clearSelection();
        this.updateConnectedItems();
        this.redrawConnections();
        this.updateConnectionCount();
        this.updateStatus('All connections cleared!');
    }

    updateConnectionCount() {
        document.getElementById('connectionCount').textContent = `Connections: ${this.connections.length}`;
    }

    updateStatus(message) {
        document.getElementById('statusText').textContent = message;
    }

    saveToStorage() {
        const data = {
            leftItems: this.leftItems,
            rightItems: this.rightItems,
            connections: this.connections,
            leftFileName: document.getElementById('leftFileName').textContent,
            rightFileName: document.getElementById('rightFileName').textContent
        };

        localStorage.setItem('columnMappings', JSON.stringify(data));
        this.updateStatus('Mappings saved to browser storage!');
    }

    loadFromStorage() {
        const saved = localStorage.getItem('columnMappings');
        if (!saved) {
            this.updateStatus('No saved mappings found');
            return;
        }

        try {
            const data = JSON.parse(saved);

            this.leftItems = data.leftItems || [];
            this.rightItems = data.rightItems || [];
            this.connections = data.connections || [];

            if (data.leftFileName) {
                document.getElementById('leftFileName').textContent = data.leftFileName;
            }
            if (data.rightFileName) {
                document.getElementById('rightFileName').textContent = data.rightFileName;
            }

            this.renderItems('leftItems', this.leftItems, 'left');
            this.renderItems('rightItems', this.rightItems, 'right');
            this.updateConnectedItems();
            this.redrawConnections();
            this.updateConnectionCount();

            this.updateStatus('Mappings loaded from storage!');
        } catch (error) {
            this.updateStatus('Error loading saved mappings');
            console.error('Error loading from storage:', error);
        }
    }
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM Content Loaded');
    // Add a small delay to ensure all elements are ready
    setTimeout(() => {
        console.log('Creating ColumnMappingApp instance');
        new ColumnMappingApp();
    }, 100);
});
