<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>File Input Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ccc; }
        input[type="file"] { margin: 10px 0; }
    </style>
</head>
<body>
    <h1>File Input Test</h1>
    
    <div class="test-section">
        <h2>Test 1: Basic File Input</h2>
        <input type="file" id="test1" accept=".csv">
        <div id="test1-result"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 2: File Input with Bootstrap Classes</h2>
        <input type="file" id="test2" class="form-control" accept=".csv">
        <div id="test2-result"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 3: Programmatic Click</h2>
        <button onclick="document.getElementById('test3').click()">Click to Open File Dialog</button>
        <input type="file" id="test3" accept=".csv" style="display: none;">
        <div id="test3-result"></div>
    </div>
    
    <div class="test-section">
        <h2>Console Output</h2>
        <div id="console-output"></div>
    </div>

    <script>
        console.log('Script loaded');
        
        function logToPage(message) {
            const output = document.getElementById('console-output');
            output.innerHTML += '<div>' + new Date().toLocaleTimeString() + ': ' + message + '</div>';
            console.log(message);
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            logToPage('DOM Content Loaded');
            
            // Test all file inputs
            ['test1', 'test2', 'test3'].forEach(function(id) {
                const input = document.getElementById(id);
                if (input) {
                    logToPage('Found input: ' + id);
                    
                    input.addEventListener('click', function() {
                        logToPage('Input ' + id + ' clicked');
                    });
                    
                    input.addEventListener('change', function(e) {
                        const files = e.target.files;
                        logToPage('Input ' + id + ' changed, files: ' + files.length);
                        const resultDiv = document.getElementById(id + '-result');
                        if (files.length > 0) {
                            resultDiv.innerHTML = 'Selected: ' + files[0].name;
                        }
                    });
                } else {
                    logToPage('Input not found: ' + id);
                }
            });
        });
        
        // Check for any errors
        window.addEventListener('error', function(e) {
            logToPage('ERROR: ' + e.message);
        });
        
        // Check for CSP violations
        document.addEventListener('securitypolicyviolation', function(e) {
            logToPage('CSP Violation: ' + e.violatedDirective + ' - ' + e.blockedURI);
        });
    </script>
</body>
</html>
