<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- Content Security Policy removed for local development -->
    <title>Drag & Drop Column Mappings</title>
    <!-- Bootstrap CSS restored -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Backup CDN for Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-icons/1.11.3/font/bootstrap-icons.min.css">

</head>
<body class="bg-light">
    <div class="container-fluid py-4">
        <!-- Header -->
        <div class="text-center mb-4">
            <h1 class="display-4 text-primary mb-2">
                <i class="bi bi-diagram-3"></i> Column Mapping Tool
            </h1>
            <p class="lead text-muted">Import CSV files and draw connections between columns</p>
        </div>

        <!-- Main Content -->
        <div class="row g-3 position-relative" style="min-height: 500px;">
            <!-- Left Panel -->
            <div class="col-md-5">
                <div class="card h-100 shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-file-earmark-arrow-up"></i> Source Columns
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3 p-3 bg-light rounded">
                            <div class="mb-2">
                                <input type="file" id="leftFileInput" class="form-control" accept=".csv">
                            </div>
                            <small id="leftFileName" class="text-muted fst-italic">No file selected</small>
                        </div>
                        <div id="leftItems" class="border rounded p-3" style="min-height: 300px; max-height: 400px; overflow-y: auto;">
                            <div class="text-center text-muted fst-italic py-5">
                                <i class="bi bi-upload fs-1 d-block mb-2"></i>
                                Import a CSV file to see columns
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Canvas for drawing lines -->
            <div class="col-md-2 d-flex align-items-center justify-content-center">
                <canvas id="connectionCanvas" class="position-absolute top-0 start-0 w-100 h-100" style="pointer-events: auto; z-index: 10;"></canvas>
                <div class="text-center text-muted">
                    <i class="bi bi-arrow-left-right fs-1"></i>
                    <div class="small mb-1">Click left column to start drawing a line</div>
                    <div class="small mb-1">Move mouse and click right column to connect</div>
                    <div class="small"><strong>OR</strong> drag from left to right column</div>
                    <div class="small text-info">Press ESC to cancel drawing</div>
                </div>
            </div>

            <!-- Right Panel -->
            <div class="col-md-5">
                <div class="card h-100 shadow-sm">
                    <div class="card-header bg-success text-white">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-file-earmark-arrow-down"></i> Target Columns
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3 p-3 bg-light rounded">
                            <div class="mb-2">
                                <input type="file" id="rightFileInput" class="form-control" accept=".csv">
                            </div>
                            <small id="rightFileName" class="text-muted fst-italic">No file selected</small>
                        </div>
                        <div id="rightItems" class="border rounded p-3" style="min-height: 300px; max-height: 400px; overflow-y: auto;">
                            <div class="text-center text-muted fst-italic py-5">
                                <i class="bi bi-upload fs-1 d-block mb-2"></i>
                                Import a CSV file to see columns
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Controls -->
        <div class="text-center mt-4">
            <div class="btn-group" role="group">
                <button id="clearAllBtn" class="btn btn-outline-danger">
                    <i class="bi bi-trash"></i> Clear All Connections
                </button>
                <button id="saveBtn" class="btn btn-outline-primary">
                    <i class="bi bi-save"></i> Save Mappings
                </button>
                <button id="loadBtn" class="btn btn-outline-secondary">
                    <i class="bi bi-folder-open"></i> Load Mappings
                </button>
            </div>
        </div>

        <!-- Status Bar -->
        <div class="mt-4">
            <div class="card">
                <div class="card-body py-2">
                    <div class="row align-items-center">
                        <div class="col">
                            <small class="text-muted">
                                <i class="bi bi-info-circle"></i>
                                <span id="statusText">Ready to import CSV files</span>
                            </small>
                        </div>
                        <div class="col-auto">
                            <span class="badge bg-primary" id="connectionCount">Connections: 0</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS restored -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Debug file input functionality
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, checking file inputs...');
            const leftInput = document.getElementById('leftFileInput');
            const rightInput = document.getElementById('rightFileInput');
            console.log('Left input:', leftInput);
            console.log('Right input:', rightInput);

            if (leftInput) {
                leftInput.addEventListener('click', function() {
                    console.log('Left file input clicked');
                });
            }
            if (rightInput) {
                rightInput.addEventListener('click', function() {
                    console.log('Right file input clicked');
                });
            }
        });
    </script>
    <script src="script.js"></script>
</body>
</html>
