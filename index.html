<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- No Content Security Policy for local development -->
    <title>Drag & Drop Column Mappings</title>
    <!-- Bootstrap CSS restored -->
    <link rel="stylesheet" href="styles.css">

</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header>
            <h1>
                <span class="icon">⚡</span> Column Mapping Tool
            </h1>
            <p>Import CSV files and draw connections between columns</p>
        </header>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Left Panel -->
            <div class="panel">
                <div class="panel-header">
                    <h2>
                        <span class="icon">📤</span> Source Columns
                    </h2>
                    <div class="import-section">
                        <input type="file" id="leftFileInput" class="file-input" accept=".csv">
                        <label for="leftFileInput" class="file-trigger-btn">Choose Left File</label>
                        <span id="leftFileName" class="file-name">No file selected</span>
                    </div>
                </div>
                <div class="items-container">
                    <div id="leftItems" class="items-list">
                        <div class="placeholder">
                            <span class="icon">📁</span>
                            Import a CSV file to see columns
                        </div>
                    </div>
                </div>
            </div>

            <!-- Canvas for drawing lines -->
            <div class="canvas-container">
                <canvas id="connectionCanvas"></canvas>
                <div class="connection-instructions">
                    <span class="icon">↔️</span>
                    <div>Click left column to start drawing a line</div>
                    <div>Move mouse and click right column to connect</div>
                    <div><strong>OR</strong> drag from left to right column</div>
                    <div class="help-text">Press ESC to cancel drawing</div>
                </div>
            </div>

            <!-- Right Panel -->
            <div class="panel">
                <div class="panel-header">
                    <h2>
                        <span class="icon">📥</span> Target Columns
                    </h2>
                    <div class="import-section">
                        <input type="file" id="rightFileInput" class="file-input" accept=".csv">
                        <label for="rightFileInput" class="file-trigger-btn">Choose Right File</label>
                        <span id="rightFileName" class="file-name">No file selected</span>
                    </div>
                </div>
                <div class="items-container">
                    <div id="rightItems" class="items-list">
                        <div class="placeholder">
                            <span class="icon">📁</span>
                            Import a CSV file to see columns
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Controls -->
        <div class="controls">
            <button id="clearAllBtn" class="control-btn danger">
                <span class="icon">🗑️</span> Clear All Connections
            </button>
            <button id="saveBtn" class="control-btn primary">
                <span class="icon">💾</span> Save Mappings
            </button>
            <button id="loadBtn" class="control-btn secondary">
                <span class="icon">📁</span> Load Mappings
            </button>
        </div>

        <!-- Status Bar -->
        <div class="status-bar">
            <div class="status-text">
                <span class="icon">ℹ️</span>
                <span id="statusText">Ready to import CSV files</span>
            </div>
            <div class="connection-count">
                <span id="connectionCount">Connections: 0</span>
            </div>
        </div>
    </div>

    <!-- Bootstrap removed - using custom CSS -->
    <script>
        // File input trigger functionality
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, setting up file input triggers...');

            const leftInput = document.getElementById('leftFileInput');
            const rightInput = document.getElementById('rightFileInput');
            const leftBtn = document.getElementById('leftFileBtn');
            const rightBtn = document.getElementById('rightFileBtn');

            console.log('Elements found:', {
                leftInput: !!leftInput,
                rightInput: !!rightInput,
                leftBtn: !!leftBtn,
                rightBtn: !!rightBtn
            });

            if (leftBtn && leftInput) {
                leftBtn.addEventListener('click', function() {
                    console.log('Left button clicked, triggering file input...');
                    leftInput.click();
                });
            }

            if (rightBtn && rightInput) {
                rightBtn.addEventListener('click', function() {
                    console.log('Right button clicked, triggering file input...');
                    rightInput.click();
                });
            }

            // Also add change listeners to see if files are selected
            if (leftInput) {
                leftInput.addEventListener('change', function(e) {
                    console.log('Left file changed:', e.target.files);
                });
            }

            if (rightInput) {
                rightInput.addEventListener('change', function(e) {
                    console.log('Right file changed:', e.target.files);
                });
            }
        });
    </script>
    <script src="script.js"></script>
</body>
</html>
