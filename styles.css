/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    line-height: 1.6;
}

.app-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* Main content layout */
.main-content {
    display: flex;
    gap: 20px;
    flex: 1;
    position: relative;
    min-height: 500px;
    margin-bottom: 30px;
}

/* Panel styles */
.panel {
    flex: 1;
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.panel-header {
    background: #f8f9fa;
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
}

.panel-header h2 {
    margin-bottom: 15px;
    color: #495057;
    font-size: 1.3rem;
}

.import-section {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
    margin-bottom: 15px;
}

.file-input {
    background: #4a90e2;
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    min-width: 140px;
    box-shadow: 0 2px 8px rgba(74, 144, 226, 0.3);
}

.file-input:hover {
    background: #357abd;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(74, 144, 226, 0.4);
}

.file-input::file-selector-button {
    background: transparent;
    color: white;
    border: none;
    padding: 0;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    margin-right: 8px;
}

.file-input::-webkit-file-upload-button {
    background: transparent;
    color: white;
    border: none;
    padding: 0;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    margin-right: 8px;
}

.file-name {
    color: #6c757d;
    font-size: 0.9rem;
    font-style: italic;
    flex: 1;
    min-width: 150px;
}

/* Items container */
.items-container {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    max-height: 400px;
}

.items-list {
    min-height: 300px;
}

.placeholder {
    text-align: center;
    color: #6c757d;
    font-style: italic;
    padding: 60px 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}

.placeholder .icon {
    font-size: 3rem;
    opacity: 0.5;
}

.item {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 12px 16px;
    margin-bottom: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    user-select: none;
}

.item:hover {
    background: #e3f2fd;
    border-color: #2196f3;
    transform: translateX(2px);
}

.item.selected {
    background: #bbdefb;
    border-color: #1976d2;
    box-shadow: 0 2px 8px rgba(25,118,210,0.3);
}

.item.connected {
    border-left: 4px solid #4caf50;
    background: #f0f8f0;
}

.item.selected {
    background: #e3f2fd;
    border-color: #2196f3;
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3);
}

.item-content {
    display: flex;
    align-items: center;
    gap: 10px;
}

.item-icon {
    font-size: 1rem;
    min-width: 20px;
}

.item-text {
    flex: 1;
}

/* Canvas container */
.canvas-container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
}

#connectionCanvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: auto;
}

.connection-instructions {
    text-align: center;
    color: #6c757d;
    font-size: 0.85rem;
    background: rgba(255, 255, 255, 0.9);
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    max-width: 200px;
    line-height: 1.4;
}

.connection-instructions .icon {
    font-size: 2rem;
    display: block;
    margin-bottom: 10px;
}

.connection-instructions .help-text {
    color: #4a90e2;
    font-size: 0.75rem;
    margin-top: 8px;
}

/* Controls */
.controls {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin: 30px 0;
    flex-wrap: wrap;
}

.control-btn {
    background: white;
    color: #495057;
    border: 2px solid #dee2e6;
    padding: 12px 24px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 8px;
}

.control-btn:hover {
    background: #f8f9fa;
    border-color: #adb5bd;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.control-btn.danger:hover {
    background: #f8d7da;
    border-color: #dc3545;
    color: #721c24;
}

.control-btn.primary:hover {
    background: #d1ecf1;
    border-color: #4a90e2;
    color: #0c5460;
}

.control-btn.secondary:hover {
    background: #e2e3e5;
    border-color: #6c757d;
    color: #383d41;
}

/* Status bar */
.status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(255,255,255,0.15);
    color: white;
    padding: 15px 25px;
    border-radius: 12px;
    margin-top: 20px;
    font-size: 0.9rem;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.status-text {
    display: flex;
    align-items: center;
    gap: 8px;
}

.connection-count {
    background: rgba(255, 255, 255, 0.2);
    padding: 6px 12px;
    border-radius: 20px;
    font-weight: 500;
    font-size: 0.85rem;
}

/* Icon styles */
.icon {
    display: inline-block;
    font-style: normal;
    font-size: 1.2em;
}

/* Responsive design */
@media (max-width: 768px) {
    .app-container {
        padding: 15px;
    }

    .main-content {
        flex-direction: column;
        gap: 15px;
    }

    .panel {
        min-height: 300px;
    }

    .import-section {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .file-name {
        text-align: center;
        margin-top: 5px;
    }

    .controls {
        flex-direction: column;
        align-items: center;
        gap: 10px;
    }

    .control-btn {
        width: 100%;
        max-width: 250px;
        justify-content: center;
    }

    .status-bar {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }

    .connection-instructions {
        font-size: 0.8rem;
        padding: 15px;
        max-width: 180px;
    }
}

/* Animation for connections */
@keyframes connectionPulse {
    0% { opacity: 0.7; }
    50% { opacity: 1; }
    100% { opacity: 0.7; }
}

.connection-line {
    animation: connectionPulse 2s infinite;
}

/* Drag and Drop styles */
.item[draggable="true"] {
    cursor: grab;
}

.item[draggable="true"]:active {
    cursor: grabbing;
}

.item.dragging {
    opacity: 0.7;
    transform: rotate(2deg);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

.item.drag-over {
    background: #fff3cd;
    border-color: #ffc107;
    transform: scale(1.02);
}

.item.drop-target {
    background: #d1ecf1;
    border-color: #17a2b8;
    animation: dropTargetPulse 1s infinite;
}

@keyframes dropTargetPulse {
    0% { box-shadow: 0 0 0 0 rgba(23, 162, 184, 0.4); }
    70% { box-shadow: 0 0 0 10px rgba(23, 162, 184, 0); }
    100% { box-shadow: 0 0 0 0 rgba(23, 162, 184, 0); }
}
